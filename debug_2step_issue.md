# Debug Guide for 2-Step Grading "undefined" Issue

## Problem
The left panel (answer panel) shows "undefined" instead of the highlighted answer.

## Debugging Steps

### 1. Check Browser Console
Open browser developer tools (F12) and look for the debug logs we added:

**Expected logs in console:**
```
🔍 DEBUG: Stored answer for partId X: [answer text]
🔍 DEBUG: Starting Step 2 - Fetching highlighted answer
🔍 DEBUG: questionId: X partId: Y
🔍 DEBUG: answer: [answer text]
🔍 DEBUG: window.currentAnswerData: {...}
🔍 DEBUG: answerToSend: [answer text]
🔍 DEBUG: Response status: 200
🔍 DEBUG: Response data: {...}
```

### 2. Check Server Logs
Look for these debug messages in the server logs:

**Expected logs in server:**
```
🔍 DEBUG: get_highlighted_answer called - question_id: X, part_id: Y
🔍 DEBUG: Part data found - input_type: [type]
🔍 DEBUG: Received answer: '[answer]', image_file: False
🔍 DEBUG: Calling grading function with answer: '[answer]...'
🔍 DEBUG: Grading completed. Evaluated points count: X
🔍 DEBUG: Creating highlighted answer with X evaluated points
🔍 DEBUG: create_highlighted_answer_parallel called with X points
🔍 DEBUG: user_answer length: X
🔍 DEBUG: Processing point 0: {...}
🔍 DEBUG: Total highlight_spans collected: X
🔍 DEBUG: Highlighted answer created. Length: X
🔍 DEBUG: Highlighted answer preview: [preview]...
🔍 DEBUG: Returning response: {...}
```

### 3. Possible Issues and Solutions

#### Issue 1: Answer not being stored properly
**Symptom:** `window.currentAnswerData` is undefined or empty
**Solution:** Check if the answer storage happens before the setTimeout

#### Issue 2: API call failing
**Symptom:** Network error or 4xx/5xx response
**Solution:** Check authentication, rate limiting, or server errors

#### Issue 3: Empty response from highlighting function
**Symptom:** `highlightData.answer` is undefined or null
**Solution:** Check if evaluated_points are empty or highlighting logic fails

#### Issue 4: HTML escaping issues
**Symptom:** Answer contains escaped HTML that displays as "undefined"
**Solution:** Check if HTML is being double-escaped

### 4. Quick Fixes to Try

#### Fix 1: Add null check in frontend
```javascript
if (highlightData.answer && highlightData.answer !== 'undefined' && highlightData.answer !== null) {
    userAnswerDiv.innerHTML = highlightData.answer;
} else {
    userAnswerDiv.innerHTML = `<div class="text-sm text-gray-700">${answerToSend}</div>`;
}
```

#### Fix 2: Ensure answer is always a string
```javascript
const answerToSend = String(window.currentAnswerData && window.currentAnswerData[partId] 
    ? window.currentAnswerData[partId] 
    : answer || '');
```

#### Fix 3: Add server-side null check
```python
if highlighted_answer is None or highlighted_answer == 'undefined':
    highlighted_answer = html.escape(user_answer)
```

### 5. Test Cases

1. **Test with simple text answer:** "This is a test answer"
2. **Test with empty answer:** ""
3. **Test with special characters:** "Answer with <tags> & symbols"
4. **Test with LaTeX:** "Answer with $x^2$ math"

### 6. Debugging Commands

#### Check if endpoint exists:
```bash
curl -X POST http://localhost:5000/get_highlighted_answer/1/1 \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "answer=test"
```

#### Check browser network tab:
- Look for the POST request to `/get_highlighted_answer/X/Y`
- Check request payload
- Check response body

### 7. Common Causes

1. **Timing issue:** Second API call happens before first one completes
2. **Session issue:** User not authenticated for second call
3. **Data type issue:** Answer is not a string
4. **HTML rendering issue:** innerHTML not handling the content properly
5. **Server error:** Exception in highlighting function

### 8. Next Steps

If the issue persists:
1. Reduce the delay from 1000ms to 100ms to test timing
2. Add more granular logging in the highlighting function
3. Test with a simple hardcoded response first
4. Check if the issue is specific to certain question types
