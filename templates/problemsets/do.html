{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- Add required libraries for Markdown and LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js"></script>

<!-- Custom styles for LaTeX rendering -->
<style>
    /* Ensure inline LaTeX is properly aligned with text */
    .katex-inline {
        display: inline-block;
        vertical-align: middle;
    }

    /* Add some spacing around inline LaTeX */
    .katex {
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing */
    .katex-display {
        margin: 1em 0;
    }

    /* Make sure inline LaTeX doesn't break the line flow */
    p {
        display: block;
        line-height: 1.5;
        margin: 1em 0;
    }

    /* Ensure inline elements are properly aligned */
    .katex-html {
        display: inline-block;
        vertical-align: middle;
    }

    /* Style for our custom inline math wrapper */
    .inline-math {
        display: inline;
        vertical-align: baseline;
        margin: 0 0.1em;
    }

    /* Style for code blocks in markdown */
    pre {
        background-color: #f5f5f5;
        padding: 0.5em;
        border-radius: 0.25em;
        overflow-x: auto;
    }

    /* Style for inline code in markdown */
    code {
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 0.25em;
        font-family: monospace;
    }

    /* Style for paragraphs in the preview */
    p {
        margin-bottom: 1em;
    }

    /* Style for headings in the preview */
    h1, h2, h3, h4, h5, h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: bold;
    }

    /* Style for lists in the preview */
    ul, ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
    }
</style>
{% endblock %}

{% block content %}
<!-- Toast Notification -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50 space-y-2"></div>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Progress Bar -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 transform hover:shadow-xl transition-all duration-300 border border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 tracking-tight">{{ problemset.name }}</h1>
                    {% if problemset.description %}
                    <p class="mt-2 text-gray-600 text-lg">{{ problemset.description }}</p>
                    {% endif %}
                    {% if problemset.pdf_attachment %}
                    <div class="mt-2">
                        <a href="{{ url_for('serve.serve_file', filename=problemset.pdf_attachment) }}" target="_blank" class="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-file-pdf mr-1.5"></i>
                            View Original PDF Document
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Created by: {{ problemset.creator.username }}</p>
                    <p class="text-sm text-gray-500 mt-1">Questions: <span id="completed-count" class="font-semibold text-indigo-600">0</span>/{{ problemset.questions|length }}</p>
                </div>
            </div>
            <div class="relative pt-1">
                <div class="flex mb-2 items-center justify-between">
                    <div>
                        <span class="text-xs font-semibold inline-block py-1.5 px-3 uppercase rounded-full text-indigo-600 bg-indigo-100" id="progress-text">
                            0% Complete
                        </span>
                    </div>
                    <div class="text-right">
                        <span class="text-xs font-semibold inline-block text-gray-600" id="time-elapsed">
                            Time: 00:00:00
                        </span>
                    </div>
                </div>
                <div class="overflow-hidden h-3 mb-4 text-xs flex rounded-full bg-indigo-100">
                    <div id="progress-bar" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-indigo-500 to-indigo-600 transition-all duration-500 ease-out" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- Past Submissions Overview -->
        {% if past_submissions %}
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 transform hover:shadow-xl transition-all duration-300 border border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Your Past Submissions</h3>
                <div class="flex space-x-4">
                    <button type="button" onclick="toggleAllSubmissions()" class="text-sm text-indigo-600 hover:text-indigo-800 flex items-center">
                        <span id="toggle-all-text">Show</span> <i class="fas fa-chevron-down ml-1" id="toggle-all-icon"></i>
                    </button>
                </div>
            </div>
            <div id="all-past-submissions" class="hidden">
                <div class="overflow-hidden overflow-x-auto rounded-lg border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Date</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Score</th>
                                <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            {% for submission in past_submissions %}
                            <tr>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6">{{ submission.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm">
                                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium
                                        {% if submission.status == 'completed' %}
                                            bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                                        {% elif submission.status == 'in_progress' %}
                                            bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                                        {% else %}
                                            bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-600/20
                                        {% endif %}">
                                        {{ submission.status|title }}
                                    </span>
                                </td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium
                                        {% if submission.total_score == submission.max_score %}
                                            bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                                        {% elif submission.total_score > 0 %}
                                            bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                                        {% else %}
                                            bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20
                                        {% endif %}">
                                        {{ submission.total_score }}/{{ submission.max_score }}
                                        {% if submission.max_score > 0 %}
                                        ({{ "%.1f"|format(submission.total_score / submission.max_score * 100) }}%)
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-right">
                                    <a href="{{ url_for('problemset_submission_details', submission_id=submission.id) }}" class="text-indigo-600 hover:text-indigo-900">View Details</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Questions List -->
        <div class="space-y-8">
            {% for question in problemset.questions %}
            <div class="bg-white rounded-2xl shadow-lg p-8 group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-200" id="question-{{ question.id }}">
                <div class="space-y-6">
                    <!-- Question Header -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-50 flex items-center justify-center border-2 transition-all duration-300" id="status-indicator-{{ question.id }}">
                                <i class="fas fa-circle text-gray-300 question-status text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">Question {{ loop.index }}</h3>
                                <p class="text-sm text-gray-500 mt-1">
                                    {% if question.topic %}
                                        {{ question.topic.subject.name }} - {{ question.topic.name }}
                                    {% else %}
                                        <span class="text-gray-400">No topic assigned</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Question Description -->
                    <div class="prose max-w-none text-gray-700">
                        <div class="question-description" data-content="{{ question.description }}"></div>
                        {% if question.attachments %}
                        <div class="mt-6">
                            {% for attachment in question.attachments %}
                            <div class="rounded-xl overflow-hidden shadow-lg mb-4">
                                <img src="{{ url_for('serve.serve_file', filename=attachment.filename) }}"
                                     alt="Question attachment"
                                     class="max-w-full h-auto max-h-[600px] mx-auto">
                            </div>
                            {% endfor %}
                        </div>
                        {% elif question.attachment %}
                        <div class="mt-6 rounded-xl overflow-hidden shadow-lg">
                            <img src="{{ url_for('serve.serve_file', filename=question.attachment) }}"
                                 alt="Question attachment"
                                 class="max-w-full h-auto max-h-[600px] mx-auto">
                        </div>
                        {% endif %}
                    </div>

                    <!-- Question Parts -->
                    <div class="mt-8 space-y-6">
                        {% for part in question.parts %}
                        <div class="bg-gray-50 rounded-xl p-6 transform hover:bg-gray-100 transition-all duration-300 border border-gray-200" id="part-{{ part.id }}">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Part {{ loop.index }}</h4>
                                <span class="text-sm text-gray-500">{{ part.score }} marks</span>
                            </div>
                            <div class="prose max-w-none text-gray-600 mb-6">
                                <div class="part-description" data-content="{{ part.description }}"></div>
                                {% if part.attachments %}
                                <div class="mt-4">
                                    {% for attachment in part.attachments %}
                                    <div class="rounded-lg overflow-hidden shadow-lg mb-4">
                                        <img src="{{ url_for('serve.serve_file', filename=attachment.filename) }}"
                                             alt="Part attachment"
                                             class="max-w-full h-auto max-h-[400px] mx-auto">
                                    </div>
                                    {% endfor %}
                                </div>
                                {% elif part.attachment %}
                                <div class="mt-4 rounded-lg overflow-hidden shadow-lg">
                                    <img src="{{ url_for('serve.serve_file', filename=part.attachment) }}"
                                         alt="Part attachment"
                                         class="max-w-full h-auto max-h-[400px] mx-auto">
                                </div>
                                {% endif %}
                            </div>
                            <div class="space-y-4">
                                <div>
                                    {% if part.input_type == 'mcq' %}
                                    <!-- MCQ Input -->
                                    <div class="mcq-input">
                                        <fieldset>
                                            <legend class="block text-sm font-medium text-gray-700 mb-3">Select the correct answer:</legend>
                                            <div class="space-y-3" id="mcq_options_{{ part.id }}">
                                                {% for option in part.options %}
                                                <div class="mcq-option relative flex items-start">
                                                    <div class="flex items-center h-5">
                                                        <input
                                                            id="mcq_option_{{ part.id }}_{{ loop.index0 }}"
                                                            name="answer-{{ part.id }}"
                                                            type="radio"
                                                            value="{{ loop.index0 }}"
                                                            class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500 answer-input"
                                                            data-part-id="{{ part.id }}"
                                                            data-question-id="{{ question.id }}"
                                                            {% if part_submissions and part.id in part_submissions and part_submissions[part.id].answer == loop.index0|string %}checked{% endif %}
                                                        >
                                                    </div>
                                                    <div class="ml-3 text-sm">
                                                        <label for="mcq_option_{{ part.id }}_{{ loop.index0 }}" class="font-medium text-gray-700">
                                                            <div class="option-content">{{ option.description | safe }}</div>
                                                        </label>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </fieldset>
                                    </div>
                                    {% else %}
                                    <!-- SAQ Input (default) -->
                                    <label for="answer-{{ part.id }}" class="block text-sm font-medium text-gray-700 mb-2">Your Answer</label>
                                    <div class="relative">
                                        <textarea
                                            id="answer-{{ part.id }}"
                                            name="answer-{{ part.id }}"
                                            rows="4"
                                            class="shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-xl answer-input transition-all duration-200 bg-white"
                                            placeholder="Enter your answer here..."
                                            data-part-id="{{ part.id }}"
                                            data-question-id="{{ question.id }}"
                                        >{% if part_submissions and part.id in part_submissions %}{{ part_submissions[part.id].answer }}{% endif %}</textarea>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Past Submissions Section -->
                                {% if past_submissions %}
                                <div class="mt-4">
                                    <div class="flex items-center justify-between">
                                        <h5 class="text-sm font-medium text-gray-700">Past Submissions</h5>
                                        <button type="button" onclick="togglePastSubmissions('{{ part.id }}')" class="text-xs text-indigo-600 hover:text-indigo-800">
                                            <span id="toggle-text-{{ part.id }}">Show</span> <i class="fas fa-chevron-down" id="toggle-icon-{{ part.id }}"></i>
                                        </button>
                                    </div>
                                    <div id="past-submissions-{{ part.id }}" class="hidden mt-2 space-y-2">
                                        {% set found = false %}
                                        {% for submission in past_submissions %}
                                            {% for question_submission in submission.question_submissions %}
                                                {% if question_submission.part_id == part.id %}
                                                    {% set found = true %}
                                                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200 text-sm">
                                                        <div class="flex justify-between items-center mb-2">
                                                            <span class="text-gray-500">{{ submission.submitted_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                                            <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium
                                                                {% if question_submission.score == part.score %}
                                                                    bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                                                                {% elif question_submission.score > 0 %}
                                                                    bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                                                                {% else %}
                                                                    bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20
                                                                {% endif %}">
                                                                Score: {{ "%.1f"|format(question_submission.score) }}/{{ "%.1f"|format(part.score) }}
                                                            </span>
                                                        </div>
                                                        <div class="prose prose-sm max-w-none bg-white p-2 rounded border border-gray-200">
                                                            {{ question_submission.answer|safe }}
                                                        </div>
                                                        <div class="mt-2 text-right">
                                                            <a href="{{ url_for('problemset_submission_details', submission_id=submission.id) }}" class="text-indigo-600 hover:text-indigo-800 text-xs">
                                                                View Full Submission <i class="fas fa-external-link-alt ml-1"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        {% endfor %}
                                        {% if not found %}
                                            <div class="text-sm text-gray-500 italic">No past submissions for this part</div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                                <div class="flex justify-end">
                                    <button
                                        type="button"
                                        onclick="submitAnswer({{ part.id }}, {{ question.id }})"
                                        class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl shadow-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:-translate-y-0.5 transition-all duration-200"
                                    >
                                        <i class="fas fa-paper-plane mr-2"></i>Submit
                                    </button>
                                </div>
                            </div>
                            <div id="feedback-{{ part.id }}" class="mt-6 hidden">
                                <!-- Feedback will be dynamically inserted here -->
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Navigation Buttons -->
        <div class="mt-12 flex justify-between items-center">
            <a href="{{ url_for('list_problemsets') }}" class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-lg text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:-translate-y-0.5 transition-all duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Back to Problem Sets
            </a>
            <div class="space-x-4">
                <button type="button" id="manual-save" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl shadow-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:-translate-y-0.5 transition-all duration-200">
                    <i class="fas fa-save mr-2"></i>Save Progress
                </button>
            </div>
        </div>
    </div>
</div>

<div class="fixed bottom-8 right-8">
    <button id="submitAllBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 flex items-center space-x-2">
        <i class="fas fa-check-circle"></i>
        <span>Submit All</span>
    </button>
</div>

<script>
// Track time spent
let startTime = Date.now();
let completedQuestions = new Set();
let totalQuestions = {{ problemset.questions|length }};
let autoSaveInterval;

function updateTimer() {
    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    const hours = Math.floor(elapsed / 3600);
    const minutes = Math.floor((elapsed % 3600) / 60);
    const seconds = elapsed % 60;
    const timeEl = document.getElementById('time-elapsed');
    if (timeEl) {
        timeEl.textContent =
            `Time: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

setInterval(updateTimer, 1000);

function updateProgress() {
    const progress = totalQuestions > 0 ? (completedQuestions.size / totalQuestions) * 100 : 0;
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const completedCount = document.getElementById('completed-count');

    if (progressBar) progressBar.style.width = `${progress}%`;
    if (progressText) progressText.textContent = `${Math.round(progress)}% Complete`;
    if (completedCount) completedCount.textContent = completedQuestions.size;
}

function updateQuestionStatus(questionId, status) {
    const indicator = document.getElementById(`status-indicator-${questionId}`);
    if (!indicator) return; // Element might not exist if question list is empty
    const icon = indicator.querySelector('.question-status');
    if (!icon) return;

    // Reset classes first
    indicator.classList.remove('border-green-500', 'border-yellow-500', 'border-red-500');
    icon.classList.remove('text-green-500', 'text-yellow-500', 'text-red-500', 'text-gray-300');

    if (status === 'complete') {
        indicator.classList.add('border-green-500');
        icon.classList.add('text-green-500');
        if (!completedQuestions.has(questionId)) {
            completedQuestions.add(questionId);
            updateProgress();
        }
    } else if (status === 'partial') {
        indicator.classList.add('border-yellow-500');
        icon.classList.add('text-yellow-500');
        // Don't add to completedQuestions if only partial
    } else if (status === 'incorrect') {
        indicator.classList.add('border-red-500');
        icon.classList.add('text-red-500');
    } else { // Default / not attempted
        icon.classList.add('text-gray-300');
    }
}


async function submitAnswer(partId, questionId) {
    // Check if this is an MCQ question
    const isMcq = document.getElementById(`mcq_options_${partId}`) !== null;
    let answer;

    if (isMcq) {
        // For MCQ, get the selected radio button value
        const selectedOption = document.querySelector(`input[name="answer-${partId}"]:checked`);
        if (!selectedOption) {
            showToast('Please select an answer before submitting.', 'error');
            return Promise.reject('No answer selected for MCQ');
        }
        answer = selectedOption.value;
    } else {
        // For SAQ, get the textarea value
        const answerInput = document.getElementById(`answer-${partId}`);
        if (!answerInput) {
            console.error(`Answer input not found for part ${partId}`);
            return Promise.reject('Answer input not found');
        }
        answer = answerInput.value.trim();
        if (!answer) {
            // Don't show toast for empty answers during submitAll, just reject
            // showToast('Please enter an answer before submitting.', 'error');
            return Promise.reject('No answer provided'); // Return a rejected promise for Promise.allSettled
        }
    }

    const submitButton = document.querySelector(`button[onclick="submitAnswer(${partId}, ${questionId})"]`); // Find the specific button

    // For MCQ questions, we don't need a feedback div
    const feedbackDiv = document.getElementById(`feedback-${partId}`);
    if (!isMcq && !feedbackDiv) {
        console.error(`Feedback div not found for part ${partId}`);
        return Promise.reject('Feedback div not found');
    }

    // Disable button and show loading state
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';
    }

    // Show loading indicator
    if (!isMcq && feedbackDiv) {
        feedbackDiv.innerHTML = `
            <div class="flex items-center justify-center p-4">
                <div class="w-6 h-6 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
        `;
        feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-gray-50'; // Reset classes
        feedbackDiv.classList.remove('hidden');
    }

    try {
        const response = await fetch(`/get_git_diff/${questionId}/${partId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'answer': answer
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            // For MCQ questions, we show only the score summary without detailed feedback
            if (isMcq) {
                // Show toast notification
                if (data.score === data.max_score) {
                    updateQuestionStatus(questionId, 'complete');
                    showToast('Correct answer!', 'success');
                } else if (data.score > 0) {
                    updateQuestionStatus(questionId, 'partial');
                    showToast('Partially correct answer.', 'warning');
                } else {
                    updateQuestionStatus(questionId, 'incorrect');
                    showToast('Incorrect answer. Try again.', 'error');
                }

                // Add only the score summary for MCQ questions
                if (feedbackDiv) {
                    // Add total score summary with verdict
                    let feedbackHtml = `
                        <div class="p-4 rounded-lg ${data.score === data.max_score ? 'bg-green-50 border border-green-200' : data.score > 0 ? 'bg-yellow-50 border border-yellow-200' : 'bg-red-50 border border-red-200'}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        ${data.score === data.max_score
                                            ? '<svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
                                            : data.score > 0
                                            ? '<svg class="h-6 w-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/></svg>'
                                            : '<svg class="h-6 w-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>'
                                        }
                                    </div>
                                    <span class="text-sm font-medium ${data.score === data.max_score ? 'text-green-800' : data.score > 0 ? 'text-yellow-800' : 'text-red-800'}">
                                        ${data.score === data.max_score ? 'Correct' : data.score > 0 ? 'Partially Correct' : 'Incorrect'}
                                    </span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-medium text-gray-900 mr-2">Score:</span>
                                    <span class="text-lg font-bold ${data.score === data.max_score ? 'text-green-600' : data.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                        ${data.score} / ${data.max_score}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-end">
                                <button type="button" onclick="explainAnswer(${partId}, ${questionId})" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                                </button>
                            </div>
                            <div id="explanation-${partId}" class="mt-4 hidden">
                                <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                        <div class="explanation-loading-${partId} hidden">
                                            <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    </div>
                                    <div class="explanation-content-${partId} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed"></div>
                                </div>
                            </div>
                        </div>
                    `;

                    feedbackDiv.innerHTML = feedbackHtml;
                    feedbackDiv.className = 'mt-6 transform transition-all duration-300';
                    feedbackDiv.classList.remove('hidden');
                }

                return Promise.resolve({ status: 'success', partId: partId });
            }

            // For SAQ questions, generate detailed feedback HTML
            let feedbackHtml = `
                <h4 class="text-sm font-medium text-gray-900 mb-3">Feedback Breakdown</h4>
            `;

            if (data.marking_points && data.marking_points.length > 0) {
                // Add side-by-side panels for user answer and marking scheme
                feedbackHtml += `
                <div class="grid grid-cols-2 gap-6 mb-4">
                    <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                        <div id="user_answer_${partId}" class="text-sm text-gray-700 space-y-3">
                            <div class="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                                    <p class="text-sm text-gray-600">Loading highlighted answer...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                        <div id="marking_scheme_${partId}" class="text-sm text-gray-700 space-y-3">
                `;

                // Add marking scheme with aligned points and vertical color bar indicators
                data.marking_points.forEach((mp, index) => {
                    // Get the border class (e.g., 'border-yellow-400')
                    const borderClass = mp.color || '';

                    feedbackHtml += `
                    <div class="grid-row flex items-start p-3 rounded bg-gray-50 h-full transform transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
                        <!-- Vertical Color Bar -->
                        <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                        <div class="flex-grow flex items-start space-x-3">
                            <!-- Achieved/Not Achieved Icon -->
                            <div class="flex-shrink-0 mt-0.5">
                                <span class="flex items-center justify-center h-5 w-5 rounded-full ${mp.achieved ? 'bg-green-100 text-green-600' : mp.partial ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}">
                                    ${mp.achieved ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                                        mp.partial ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path></svg>' :
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path></svg>'
                                    }
                                </span>
                            </div>
                            <!-- Marking Point Text -->
                            <div class="flex-1">
                                <p class="text-sm text-gray-900 font-medium">
                                    [${mp.achieved_score}/${mp.score} marks] <span class="latex-content">${mp.description}</span>
                                    ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                </p>
                            </div>
                        </div>
                    </div>`;
                });

                feedbackHtml += `
                        </div>
                    </div>
                </div>
                `;

                // Store the original answer for the second API call
                window.currentAnswerData = window.currentAnswerData || {};
                window.currentAnswerData[partId] = answer;
            } else {
                 feedbackHtml += `<p class="text-sm text-gray-600 italic">No marking points defined for this part.</p>`;
            }

            // Add total score summary with verdict
            feedbackHtml += `
                <div class="mt-6 p-4 rounded-lg ${data.score === data.max_score ? 'bg-green-50 border border-green-200' : data.score > 0 ? 'bg-yellow-50 border border-yellow-200' : 'bg-red-50 border border-red-200'}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                ${data.score === data.max_score
                                    ? '<svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
                                    : data.score > 0
                                    ? '<svg class="h-6 w-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/></svg>'
                                    : '<svg class="h-6 w-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>'
                                }
                            </div>
                            <span class="text-sm font-medium ${data.score === data.max_score ? 'text-green-800' : data.score > 0 ? 'text-yellow-800' : 'text-red-800'}">
                                ${data.score === data.max_score ? 'Correct' : data.score > 0 ? 'Partially Correct' : 'Incorrect'}
                            </span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-900 mr-2">Score:</span>
                            <span class="text-lg font-bold ${data.score === data.max_score ? 'text-green-600' : data.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${data.score} / ${data.max_score}
                            </span>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button type="button" onclick="explainAnswer(${partId}, ${questionId})" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                        </button>
                    </div>
                    <div id="explanation-${partId}" class="mt-4 hidden">
                        <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation(Based off RI notes)</h4>
                                <div class="explanation-loading-${partId} hidden">
                                    <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                </div>
                            </div>
                            <div class="explanation-content-${partId} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed"></div>
                        </div>
                    </div>
                </div>
            `;

            // Show the feedback div with the generated HTML
            if (feedbackDiv) {
                feedbackDiv.innerHTML = feedbackHtml;
                feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-gray-50 border border-gray-200';
            }

            // Update background based on overall score
            if (data.score === data.max_score) {
                updateQuestionStatus(questionId, 'complete');
            } else if (data.score > 0) {
                updateQuestionStatus(questionId, 'partial');
            } else {
                updateQuestionStatus(questionId, 'incorrect'); // Explicitly mark as incorrect
            }

            // Automatically fetch highlighted answer after a short delay (Step 2 of 2-step process)
            setTimeout(async () => {
                try {
                    const highlightResponse = await fetch(`/get_highlighted_answer/${questionId}/${partId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            'answer': window.currentAnswerData[partId] || answer
                        })
                    });

                    const highlightData = await highlightResponse.json();

                    if (highlightData.status === 'success') {
                        // Update the user answer div with highlighted content
                        const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                        if (userAnswerDiv) {
                            userAnswerDiv.innerHTML = highlightData.answer;
                        }
                    } else {
                        console.error('Error fetching highlighted answer:', highlightData.message);
                        // Fallback to plain answer
                        const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                        if (userAnswerDiv) {
                            userAnswerDiv.innerHTML = `<div class="text-sm text-gray-700">${answer}</div>`;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching highlighted answer:', error);
                    // Fallback to plain answer
                    const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                    if (userAnswerDiv) {
                        userAnswerDiv.innerHTML = `<div class="text-sm text-gray-700">${answer}</div>`;
                    }
                }
            }, 1000); // 1 second delay to show marking points first

            // Add script to ensure heights match and render LaTeX
            setTimeout(() => {
                try {
                    // Render LaTeX in the marking points
                    document.querySelectorAll('.latex-content').forEach(element => {
                        renderMathInElement(element, {
                            delimiters: [
                                {left: '$$', right: '$$', display: true},
                                {left: '$', right: '$', display: false},
                                {left: '\\(', right: '\\)', display: false},
                                {left: '\\[', right: '\\]', display: true}
                            ],
                            throwOnError: false,
                            output: 'html'
                        });
                    });

                    const gridRows = document.querySelectorAll('.grid-row');
                    const rows = Array.from(gridRows);
                    const numPoints = data.marking_points.length;

                    // Match heights for each pair of marking points
                    for (let i = 0; i < numPoints; i++) {
                        const leftRow = rows[i];
                        const rightRow = rows[i + numPoints];
                        if (leftRow && rightRow) {
                            const maxHeight = Math.max(leftRow.offsetHeight, rightRow.offsetHeight);
                            leftRow.style.height = maxHeight + 'px';
                            rightRow.style.height = maxHeight + 'px';
                        }
                    }

                    // Match heights for marking points (no additional score summary needed)
                } catch (error) {
                    console.error("Error rendering LaTeX or adjusting heights:", error);
                }
            }, 100);
            return Promise.resolve({ status: 'success', partId: partId }); // Resolve promise on success

        } else {
            showToast('Error: ' + data.message, 'error');

            // For MCQ questions, we don't need to update the feedback div
            if (!isMcq && feedbackDiv) {
                feedbackDiv.innerHTML = `<p class="text-red-700 text-sm">Error processing answer: ${data.message}</p>`;
                feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-red-50 border border-red-200';
            }

            return Promise.reject(`Error processing part ${partId}: ${data.message}`); // Reject promise on API error
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('An error occurred while submitting your answer.', 'error');
        // Display error in the feedback div for non-MCQ questions
        if (!isMcq && feedbackDiv) {
            feedbackDiv.innerHTML = `<p class="text-red-700 text-sm">An unexpected error occurred. Please try again.</p>`;
            feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-red-50 border border-red-200';
        }
        return Promise.reject(`Network error for part ${partId}: ${error.message}`); // Reject promise on network error
    } finally {
        // Re-enable button in the finally block to ensure it always happens
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Submit';
        }
    }
} // End of submitAnswer function


// Toast notification system
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return; // Exit if container not found
    const toast = document.createElement('div');

    // Set base classes
    toast.className = 'flex items-center p-6 rounded-xl shadow-lg transform transition-all duration-500 translate-y-full opacity-0';

    // Set background color based on type
    switch(type) {
        case 'success':
            toast.classList.add('bg-green-50', 'border-l-4', 'border-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-50', 'border-l-4', 'border-red-500');
            break;
        default:
            toast.classList.add('bg-indigo-50', 'border-l-4', 'border-indigo-500');
    }

    // Add icon based on type
    const icon = document.createElement('i');
    icon.className = 'fas mr-4 text-xl';
    switch(type) {
        case 'success':
            icon.classList.add('fa-check-circle', 'text-green-500');
            break;
        case 'error':
            icon.classList.add('fa-exclamation-circle', 'text-red-500');
            break;
        default:
            icon.classList.add('fa-info-circle', 'text-indigo-500');
    }
    toast.appendChild(icon);

    // Add message
    const messageSpan = document.createElement('span');
    messageSpan.className = 'text-sm font-medium text-gray-700';
    messageSpan.textContent = message;
    toast.appendChild(messageSpan);

    // Add to container
    toastContainer.appendChild(toast);

    // Trigger animation
    setTimeout(() => {
        toast.classList.remove('translate-y-full', 'opacity-0');
    }, 100);

    // Remove after 4 seconds
    setTimeout(() => {
        toast.classList.add('translate-y-full', 'opacity-0');
        setTimeout(() => {
            if (toast.parentNode === toastContainer) { // Check if still attached
                 toastContainer.removeChild(toast);
            }
        }, 500);
    }, 4000);
}

// Update auto-save function
async function autoSave() {
    const answerInputs = document.querySelectorAll('.answer-input');

    for (const input of answerInputs) {
        const partId = input.dataset.partId;
        const questionId = input.dataset.questionId;
        let answer;

        // Check if this is an MCQ question (radio button)
        if (input.type === 'radio') {
            // Only save checked radio buttons
            if (!input.checked) continue;
            answer = input.value;
        } else {
            // For text inputs/textareas
            answer = input.value; // Save even if just whitespace for now
        }

        // Check if the feedback div is hidden (meaning not submitted yet)
        const feedbackDiv = document.getElementById(`feedback-${partId}`);
        if (!feedbackDiv || !feedbackDiv.classList.contains('hidden')) {
             continue; // Don't auto-save if already submitted/showing feedback
        }

        try {
            const response = await fetch('/auto_save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',  // This ensures cookies are sent with the request
                body: JSON.stringify({
                    problemset_id: {{ problemset.id }},
                    question_id: questionId,
                    part_id: partId,
                    answer: answer, // Send the raw value
                    // confidence: 'Medium' // Confidence not needed for auto-save
                })
            });
            const data = await response.json(); // Always consume the response body
            if (response.ok && data.status === 'success') {
                // Optionally add a subtle indicator that it saved, e.g., change border color briefly
            } else {
                 console.error('Auto-save failed for part:', partId, data.message || 'Unknown error');
            }
        } catch (error) {
            console.error('Auto-save network error for part:', partId, error);
            // Optionally show a subtle error indicator, but avoid annoying toasts
        }
    }
}

// Manual save function with toast notification
async function manualSave() {
    const answerInputs = document.querySelectorAll('.answer-input');
    let savedCount = 0;
    let errorOccurred = false;

    for (const input of answerInputs) {
        const partId = input.dataset.partId;
        const questionId = input.dataset.questionId;
        let answer;

        // Check if this is an MCQ question (radio button)
        if (input.type === 'radio') {
            // Only save checked radio buttons
            if (!input.checked) continue;
            answer = input.value;
        } else {
            // For text inputs/textareas
            answer = input.value; // Save raw value
        }

        try {
            const response = await fetch('/auto_save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    problemset_id: {{ problemset.id }},
                    question_id: questionId,
                    part_id: partId,
                    answer: answer,
                    // confidence: 'Medium'
                })
            });

            const data = await response.json();
            if (response.ok && data.status === 'success') {
                savedCount++; // Count successful saves
            } else {
                 errorOccurred = true;
                 console.error('Manual save failed for part:', partId, data.message || `HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Manual save network error for part:', partId, error);
            errorOccurred = true;
        }
    }

    if (errorOccurred) {
         showToast('Some progress could not be saved.', 'error');
    } else if (savedCount > 0) { // Only show success if something was actually saved
        showToast(`Progress saved successfully!`, 'success');
    } else {
        showToast('No changes to save.', 'info'); // Inform user if nothing needed saving
    }
}


// Restore saved progress
async function restoreProgress() {
    try {
        const response = await fetch('/get_saved_progress/{{ problemset.id }}', {
            credentials: 'same-origin'  // This ensures cookies are sent with the request
        });
        const data = await response.json();

        if (response.ok && data.status === 'success') {
            let restoredCount = 0;
            for (const submission of data.submissions) {
                // Check if this is an MCQ question
                const mcqOptions = document.getElementById(`mcq_options_${submission.part_id}`);

                if (mcqOptions) {
                    // This is an MCQ question, find the correct radio button to check
                    const radioOption = document.querySelector(`input[name="answer-${submission.part_id}"][value="${submission.answer}"]`);
                    if (radioOption) {
                        // Only restore if the feedback div is hidden (not submitted)
                        const feedbackDiv = document.getElementById(`feedback-${submission.part_id}`);
                        if (feedbackDiv && feedbackDiv.classList.contains('hidden')) {
                            radioOption.checked = true;
                            if (submission.answer) {
                                restoredCount++;
                            }
                        }
                    }
                } else {
                    // This is a regular text answer
                    const answerInput = document.getElementById(`answer-${submission.part_id}`);
                    if (answerInput) {
                        // Only restore if the feedback div is hidden (not submitted)
                        const feedbackDiv = document.getElementById(`feedback-${submission.part_id}`);
                        if (feedbackDiv && feedbackDiv.classList.contains('hidden')) {
                            answerInput.value = submission.answer || ''; // Use empty string if null
                            if (submission.answer) {
                                // Don't update status on restore, just fill the field
                                // updateQuestionStatus(submission.question_id, 'partial');
                                restoredCount++;
                            }
                        }
                    }
                }
            }
            if (restoredCount > 0) {
                // Maybe don't show toast on load? Can be distracting.
                // showToast(`Restored progress for ${restoredCount} part(s).`, 'info');
                console.log(`Restored progress for ${restoredCount} part(s).`);
            }
        } else {
            console.error('Failed to get saved progress:', data.message || `HTTP ${response.status}`);
            // Don't show error toast on load, could be annoying
        }
    } catch (error) {
        console.error('Failed to restore progress:', error);
    }
}

// Toggle past submissions visibility
function togglePastSubmissions(partId) {
    const submissionsDiv = document.getElementById(`past-submissions-${partId}`);
    const toggleText = document.getElementById(`toggle-text-${partId}`);
    const toggleIcon = document.getElementById(`toggle-icon-${partId}`);

    if (submissionsDiv.classList.contains('hidden')) {
        submissionsDiv.classList.remove('hidden');
        toggleText.textContent = 'Hide';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        submissionsDiv.classList.add('hidden');
        toggleText.textContent = 'Show';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}

// Toggle all past submissions visibility
function toggleAllSubmissions() {
    const submissionsDiv = document.getElementById('all-past-submissions');
    const toggleText = document.getElementById('toggle-all-text');
    const toggleIcon = document.getElementById('toggle-all-icon');

    if (submissionsDiv.classList.contains('hidden')) {
        submissionsDiv.classList.remove('hidden');
        toggleText.textContent = 'Hide';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        submissionsDiv.classList.add('hidden');
        toggleText.textContent = 'Show';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}

// Function to render LaTeX with KaTeX
function renderLatex(latex, displayMode) {
    try {
        // Render the LaTeX expression
        const rendered = katex.renderToString(latex, {
            displayMode: displayMode,
            throwOnError: false,
            output: 'html'
        });

        // For inline LaTeX, add a special class to help with styling
        if (!displayMode) {
            return `<span class="inline-math">${rendered}</span>`;
        }

        return rendered;
    } catch (error) {
        console.error('Error rendering LaTeX:', error, latex);
        return `<span class="text-red-500">Error rendering LaTeX: ${latex}</span>`;
    }
}

// Process text containing both Markdown and LaTeX
function processText(text) {
    if (!text) return '';

    console.log('Processing text:', text);

    try {
        // Our approach: Process LaTeX first, then Markdown

        // Step 1: Extract and save LaTeX blocks
        let placeholders = [];
        let processedText = text;

        // Extract display math ($$...$$)
        processedText = processedText.replace(/\$\$(.*?)\$\$/gs, function (match, latex) {
            const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
            placeholders.push({
                id: id,
                latex: latex.trim(),
                displayMode: true
            });
            return `<span id="${id}" class="latex-placeholder"></span>`;
        });

        // Extract inline math ($...$)
        processedText = processedText.replace(/\$([^\$\n]+?)\$/g, function (match, latex) {
            const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
            placeholders.push({
                id: id,
                latex: latex.trim(),
                displayMode: false
            });
            return `<span id="${id}" class="latex-placeholder"></span>`;
        });

        // Step 2: Parse the text as Markdown
        let html = marked.parse(processedText);

        // Step 3: Replace placeholders with rendered LaTeX
        placeholders.forEach(placeholder => {
            const rendered = renderLatex(placeholder.latex, placeholder.displayMode);
            html = html.replace(
                new RegExp(`<span id="${placeholder.id}" class="latex-placeholder"></span>`, 'g'),
                rendered
            );
        });

        return html;
    } catch (error) {
        console.error('Error in custom processing:', error);
        return `<p class="text-red-500">Error processing text</p>`;
    }
}

// Start auto-save when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Configure marked
    if (typeof marked !== 'undefined') {
        marked.use({
            breaks: true,  // Convert line breaks to <br>
            gfm: true,    // Enable GitHub Flavored Markdown
            mangle: false, // Don't mangle email addresses
            headerIds: false // Don't add IDs to headers
        });
    }

    // We'll add MCQ option rendering to the existing rendering code below

    // Define renderMathInElement if it doesn't exist
    if (typeof renderMathInElement !== 'function' && typeof katex !== 'undefined') {
        window.renderMathInElement = function(element, options) {
            if (!element || !options) return;

            const delimiters = options.delimiters || [];
            const text = element.textContent;

            // Process each delimiter type
            delimiters.forEach(delimiter => {
                const leftDelim = delimiter.left;
                const rightDelim = delimiter.right;
                const displayMode = delimiter.display || false;

                // Create a regex to find all instances of this delimiter
                const regex = new RegExp(`${escapeRegExp(leftDelim)}(.*?)${escapeRegExp(rightDelim)}`, 'g');

                // Replace all instances with rendered LaTeX
                const html = element.innerHTML.replace(regex, (match, latex) => {
                    try {
                        return katex.renderToString(latex.trim(), {
                            displayMode: displayMode,
                            throwOnError: false
                        });
                    } catch (e) {
                        console.error('Error rendering LaTeX:', e);
                        return match; // Return the original text if there's an error
                    }
                });

                element.innerHTML = html;
            });
        };

        // Helper function to escape regex special characters
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    }

    // Render part descriptions
    const partDescriptions = document.querySelectorAll('.part-description');
    console.log('Found part descriptions:', partDescriptions.length);

    partDescriptions.forEach(element => {
        const content = element.getAttribute('data-content');
        if (content) {
            const processedHTML = processText(content);
            element.innerHTML = processedHTML;
        }
    });

    // Render question descriptions
    const questionDescriptions = document.querySelectorAll('.question-description');
    console.log('Found question descriptions:', questionDescriptions.length);

    questionDescriptions.forEach(element => {
        const content = element.getAttribute('data-content');
        if (content) {
            const processedHTML = processText(content);
            element.innerHTML = processedHTML;
        }
    });

    // Render MCQ option content
    const mcqOptions = document.querySelectorAll('.option-content');
    console.log('Found MCQ options:', mcqOptions.length);

    mcqOptions.forEach(element => {
        const content = element.textContent;
        if (content) {
            const processedHTML = processText(content);
            element.innerHTML = processedHTML;
        }
    });

    // Restore saved progress first
    restoreProgress().then(() => {
        // Start auto-save interval (every 10 seconds)
        autoSaveInterval = setInterval(autoSave, 10000);
    });

    // Add manual save button event listener
    const manualSaveButton = document.getElementById('manual-save');
    if (manualSaveButton) {
        manualSaveButton.addEventListener('click', manualSave);
    }

    // Auto-save when user leaves the page
    window.addEventListener('beforeunload', (event) => {
        clearInterval(autoSaveInterval); // Stop interval first
        // Perform a final synchronous save if possible, though unreliable
        // For modern browsers, navigator.sendBeacon is better if backend supports it
        // For simplicity, we'll just call autoSave, but it might not complete
        autoSave();
        // Note: You cannot reliably prevent page unload or guarantee async operations complete here.
    });

    // Add event listener for the submit all button
    const submitAllBtn = document.getElementById('submitAllBtn');
    if (submitAllBtn) {
        submitAllBtn.addEventListener('click', submitAll);
    }

    // Add smooth scroll behavior for questions
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetElement = document.querySelector(this.getAttribute('href'));
            if (targetElement) {
                 targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add intersection observer for fade-in animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('opacity-100', 'translate-y-0');
                entry.target.classList.remove('opacity-0', 'translate-y-4');
                observer.unobserve(entry.target); // Optional: stop observing once animated
            }
        });
    }, {
        threshold: 0.1 // Trigger when 10% of the element is visible
    });

    // Observe all question containers (adjust selector if needed)
    document.querySelectorAll('.space-y-8 > div[id^="question-"]').forEach(card => {
        card.classList.add('opacity-0', 'translate-y-4', 'transition-all', 'duration-500', 'ease-out');
        observer.observe(card);
    });

}); // End DOMContentLoaded

async function submitAll() {
    const answerInputs = document.querySelectorAll('.answer-input');
    const submitAllBtn = document.getElementById('submitAllBtn');
    if (!submitAllBtn) return; // Exit if button not found

    // Disable the submit button and show loading state
    submitAllBtn.disabled = true;
    submitAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting All...';

    const submissionPromises = [];

    // Group inputs by part ID to handle radio buttons correctly
    const partInputs = {};

    answerInputs.forEach(input => {
        const partId = input.dataset.partId;
        const questionId = input.dataset.questionId;

        // Initialize the part entry if it doesn't exist
        if (!partInputs[partId]) {
            partInputs[partId] = {
                partId,
                questionId,
                inputs: []
            };
        }

        // Add this input to the part's inputs
        partInputs[partId].inputs.push(input);
    });

    // Process each part
    Object.values(partInputs).forEach(part => {
        const partId = part.partId;
        const questionId = part.questionId;

        // Check if this is an MCQ question (radio buttons)
        const isMcq = part.inputs[0].type === 'radio';

        if (isMcq) {
            // For MCQ, find the selected radio button
            const selectedInput = part.inputs.find(input => input.checked);
            if (selectedInput) {
                // We have a selected answer, submit it
                submissionPromises.push(submitAnswer(partId, questionId));
            } else {
                // No selection, clear any existing feedback
                const feedbackDiv = document.getElementById(`feedback-${partId}`);
                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '';
                    feedbackDiv.classList.add('hidden');
                }
            }
        } else {
            // For text inputs, use the first (and only) input
            const input = part.inputs[0];
            const answer = input.value.trim();

            // Only attempt to submit if there is an answer
            if (answer) {
                submissionPromises.push(submitAnswer(partId, questionId));
            } else {
                // If no answer, clear any existing feedback for this part
                const feedbackDiv = document.getElementById(`feedback-${partId}`);
                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '';
                    feedbackDiv.classList.add('hidden');
                }
            }
        }
    });

    try {
        // Wait for all individual submissions to complete (or fail)
        const results = await Promise.allSettled(submissionPromises);

        // Check if any submissions failed
        const failedSubmissions = results.filter(result => result.status === 'rejected');

        if (failedSubmissions.length > 0) {
            showToast(`Some answers could not be submitted. Please check for errors.`, 'error');
            console.error('Failed submissions:', failedSubmissions);
        } else {
            // Create a ProblemSetSubmission record
            try {
                // Collect all successful submissions
                const submittedAnswers = [];

                // Process each part to collect answers
                Object.values(partInputs).forEach(part => {
                    const partId = part.partId;
                    const questionId = part.questionId;

                    // Check if this is an MCQ question (radio buttons)
                    const isMcq = part.inputs[0].type === 'radio';

                    if (isMcq) {
                        // For MCQ, find the selected radio button
                        const selectedInput = part.inputs.find(input => input.checked);
                        if (selectedInput) {
                            submittedAnswers.push({
                                question_id: parseInt(questionId),
                                part_id: parseInt(partId),
                                answer: selectedInput.value
                            });
                        }
                    } else {
                        // For text inputs, use the first (and only) input
                        const input = part.inputs[0];
                        const answer = input.value.trim();
                        if (answer) {
                            submittedAnswers.push({
                                question_id: parseInt(questionId),
                                part_id: parseInt(partId),
                                answer: answer
                            });
                        }
                    }
                });

                // Call the API to create a ProblemSetSubmission
                const response = await fetch('/submit_problemset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        problemset_id: {{ problemset.id }},
                        submissions: submittedAnswers
                    })
                });

                const result = await response.json();
                if (response.ok && result.status === 'success') {
                    showToast('All answers submitted successfully! Your submission has been recorded.', 'success');
                    // Optionally redirect to the submission details page
                    // window.location.href = `/problemsets/submissions/${result.submission_id}`;
                } else {
                    showToast(`Error recording submission: ${result.message || 'Unknown error'}`, 'error');
                    console.error('Error recording submission:', result);
                }
            } catch (error) {
                console.error('Error creating problemset submission:', error);
                showToast('Your answers were submitted, but there was an error recording the submission.', 'warning');
            }
        }

    } catch (error) {
        // This catch block might not be strictly necessary with Promise.allSettled,
        // but good practice to have.
        console.error('Unexpected error during submitAll:', error);
        showToast('An unexpected error occurred while submitting all answers.', 'error');
    } finally {
        // Re-enable the submit button regardless of success/failure
        submitAllBtn.disabled = false;
        submitAllBtn.innerHTML = '<i class="fas fa-check-circle"></i><span>Submit All</span>'; // Restore original text/icon
    }
} // End of submitAll function

// Function to explain the answer using Gemini LLM
async function explainAnswer(partId, questionId) {
    const explanationDiv = document.getElementById(`explanation-${partId}`);
    const explanationContent = document.querySelector(`.explanation-content-${partId}`);
    const loadingIndicator = document.querySelector(`.explanation-loading-${partId}`);

    if (!explanationDiv || !explanationContent || !loadingIndicator) {
        console.error('Required explanation elements not found');
        return;
    }

    // If already visible, toggle it off and return
    if (!explanationDiv.classList.contains('hidden')) {
        explanationDiv.classList.add('hidden');
        return;
    }

    // Show the explanation div and loading indicator
    explanationDiv.classList.remove('hidden');
    loadingIndicator.classList.remove('hidden');
    explanationContent.innerHTML = '<div class="text-gray-500 text-sm italic">Generating explanation...</div>';

    try {
        // Fetch the explanation from the server
        const response = await fetch(`/explain_answer/${questionId}/${partId}`, {
            method: 'GET'
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        // Set up streaming response with real-time display
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let explanation = '';

        // Hide loading and show content immediately
        loadingIndicator.classList.add('hidden');
        explanationContent.innerHTML = '<div class="streaming-content whitespace-pre-wrap font-mono text-sm"></div>';
        const streamingDiv = explanationContent.querySelector('.streaming-content');

        // Process the stream
        while (true) {
            const { value, done } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            explanation += chunk;

            // Show raw text immediately as it streams in
            streamingDiv.textContent = explanation;

            // Scroll to show new content immediately
            explanationContent.scrollTop = explanationContent.scrollHeight;
        }

        // After streaming is complete, apply full formatting
        let formattedExplanation = explanation
            // Add proper styling to headings
            .replace(/^# (.*?)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h3>')
            .replace(/^## (.*?)$/gm, '<h4 class="text-md font-semibold text-gray-800 mt-3 mb-2">$1</h4>')
            .replace(/^### (.*?)$/gm, '<h5 class="text-sm font-semibold text-gray-800 mt-2 mb-1">$1</h5>')
            // Style bullet points with • symbol
            .replace(/^• (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
            // Style numbered lists
            .replace(/^\d+\. (.*?)$/gm, '<li class="ml-4 list-decimal list-inside mb-1">$1</li>')
            // Style traditional markdown lists (fallback)
            .replace(/^\* (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
            .replace(/^- (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
            // Remove any remaining markdown bold/italic formatting and replace with proper HTML
            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
            .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
            // Handle code blocks (though we discourage them in prompts)
            .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
            // Wrap paragraphs (but not headings or list items)
            .replace(/^(?!<h|<li|<p|<div|<strong|<em|<code)(.*?)$/gm, '<p class="mb-2 leading-relaxed">$1</p>');

        // Replace with formatted content
        explanationContent.innerHTML = formattedExplanation;

        // Render LaTeX only once at the end
        renderMathInElement(explanationContent, {
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            output: 'html'
        });
    } catch (error) {
        console.error('Error fetching explanation:', error);
        explanationContent.innerHTML = `<p class="text-red-600">Error: ${error.message}</p>`;
    } finally {
        // Hide loading indicator
        loadingIndicator.classList.add('hidden');
    }
}

</script>

<style>
/* Enhanced animations */
@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced evidence text styling */
.evidence-text {
    position: relative;
    border-left: 3px solid currentColor;
    transition: all 0.2s ease;
    line-height: 1.6;
}

.evidence-text:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Explanation styling */
[class^="explanation-content-"] h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.25rem;
}

[class^="explanation-content-"] h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #4b5563;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
}

[class^="explanation-content-"] ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
    list-style-type: disc;
}

[class^="explanation-content-"] li {
    margin-bottom: 0.25rem;
}

[class^="explanation-content-"] p {
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

[class^="explanation-content-"] .katex {
    font-size: 1.1em;
}

[class^="explanation-content-"] .katex-display {
    margin: 1em 0;
    overflow-x: auto;
    overflow-y: hidden;
}

/* Improved grid row styling */
.grid-row {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease-in-out;
}

.grid-row::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, rgba(99, 102, 241, 0.3), transparent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.grid-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.grid-row:hover::after {
    transform: scaleX(1);
}

@keyframes slideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(20px);
        opacity: 0;
    }
}

/* Smooth transitions */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states */
textarea:focus, button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Improved scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .rounded-2xl {
        border-radius: 1rem;
    }

    .p-8 {
        padding: 1.5rem;
    }

    .text-3xl {
        font-size: 1.875rem;
    }

    .space-y-8 {
        margin-top: 2rem;
    }
}
</style>

{% endblock %}
