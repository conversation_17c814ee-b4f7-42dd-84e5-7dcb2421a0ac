#!/usr/bin/env python3
"""
Test script to debug the highlighting function independently.
This helps isolate the issue without running the full Flask app.
"""

import html

def create_highlighted_answer_test(evaluated_points, user_answer):
    """Test version of the highlighting function with debug output"""
    print(f"🔍 DEBUG: create_highlighted_answer_test called with {len(evaluated_points)} points")
    print(f"🔍 DEBUG: user_answer: '{user_answer}'")
    print(f"🔍 DEBUG: user_answer length: {len(user_answer)}")
    
    # Collect all highlighting spans with their indices
    highlight_spans = []

    for i, point in enumerate(evaluated_points):
        print(f"🔍 DEBUG: Processing point {i}: {point}")
        if (point.get('achieved') or point.get('partial')) and point.get('evidence_indices') and point.get('color'):
            # Use a dashed border for partial answers, solid for full credit
            border_style = "border-b-2 " if point.get('achieved') else "border-b-2 border-dashed "
            color_class = border_style + point['color']

            # Create feedback text
            feedback = html.escape(point.get('description', ''))

            # Add spans for each evidence index range
            for start_idx, end_idx in point.get('evidence_indices', []):
                highlight_spans.append({
                    'start': start_idx,
                    'end': end_idx,
                    'color_class': color_class,
                    'feedback': feedback,
                    'achieved': point.get('achieved', False),
                    'partial': point.get('partial', False)
                })
                print(f"🔍 DEBUG: Added span: start={start_idx}, end={end_idx}, color={color_class}")

    # Sort spans by start position (forward order for proper processing)
    highlight_spans.sort(key=lambda x: x['start'])

    # Apply highlighting spans by building the result string
    print(f"🔍 DEBUG: Total highlight_spans collected: {len(highlight_spans)}")
    if not highlight_spans:
        print(f"🔍 DEBUG: No highlight spans, returning escaped answer")
        escaped_answer = html.escape(user_answer)
        print(f"🔍 DEBUG: Escaped answer: '{escaped_answer}'")
        return escaped_answer  # Return escaped answer for HTML safety

    # Merge overlapping spans to avoid duplication
    merged_spans = []
    for span in highlight_spans:
        if not merged_spans or span['start'] >= merged_spans[-1]['end']:
            # No overlap, add new span
            merged_spans.append(span)
        else:
            # Overlap detected, extend the previous span
            last_span = merged_spans[-1]
            if span['end'] > last_span['end']:
                # Extend the end of the last span
                last_span['end'] = span['end']
            # Keep the color and feedback from the first span for consistency

    print(f"🔍 DEBUG: Merged spans: {len(merged_spans)}")

    # Build highlighted answer by processing merged spans in forward order
    result_parts = []
    last_pos = 0

    for span in merged_spans:
        start = span['start']
        end = span['end']

        # Add text before this span (escape for HTML safety)
        if start > last_pos:
            before_text = html.escape(user_answer[last_pos:start])
            result_parts.append(before_text)
            print(f"🔍 DEBUG: Added before text: '{before_text}'")

        # Add the highlighted span (escape content for HTML safety)
        original_text = user_answer[start:end]
        escaped_text = html.escape(original_text)
        span_tag = f'<span class="{span["color_class"]}" title="{span["feedback"]}">{escaped_text}</span>'
        result_parts.append(span_tag)
        print(f"🔍 DEBUG: Added highlighted span: '{span_tag}'")

        last_pos = end

    # Add any remaining text at the end (escape for HTML safety)
    if last_pos < len(user_answer):
        remaining_text = html.escape(user_answer[last_pos:])
        result_parts.append(remaining_text)
        print(f"🔍 DEBUG: Added remaining text: '{remaining_text}'")

    result = ''.join(result_parts)
    print(f"🔍 DEBUG: Final result: '{result}'")
    print(f"🔍 DEBUG: Final result length: {len(result)}")
    return result

def test_cases():
    """Test various scenarios"""
    print("="*50)
    print("TESTING HIGHLIGHTING FUNCTION")
    print("="*50)
    
    # Test Case 1: No highlighting (empty evaluated_points)
    print("\n--- Test Case 1: No highlighting ---")
    result1 = create_highlighted_answer_test([], "This is a simple answer")
    print(f"Result 1: {result1}")
    
    # Test Case 2: Simple highlighting
    print("\n--- Test Case 2: Simple highlighting ---")
    evaluated_points2 = [{
        'achieved': True,
        'evidence_indices': [[5, 7]],  # highlight "is"
        'color': 'border-yellow-400',
        'description': 'Test point'
    }]
    result2 = create_highlighted_answer_test(evaluated_points2, "This is a simple answer")
    print(f"Result 2: {result2}")
    
    # Test Case 3: Multiple highlights
    print("\n--- Test Case 3: Multiple highlights ---")
    evaluated_points3 = [
        {
            'achieved': True,
            'evidence_indices': [[0, 4]],  # highlight "This"
            'color': 'border-yellow-400',
            'description': 'First point'
        },
        {
            'partial': True,
            'evidence_indices': [[10, 16]],  # highlight "simple"
            'color': 'border-blue-400',
            'description': 'Second point'
        }
    ]
    result3 = create_highlighted_answer_test(evaluated_points3, "This is a simple answer")
    print(f"Result 3: {result3}")
    
    # Test Case 4: No valid highlights (missing required fields)
    print("\n--- Test Case 4: Invalid highlights ---")
    evaluated_points4 = [{
        'achieved': True,
        # Missing evidence_indices and color
        'description': 'Invalid point'
    }]
    result4 = create_highlighted_answer_test(evaluated_points4, "This is a simple answer")
    print(f"Result 4: {result4}")
    
    print("\n" + "="*50)
    print("TESTING COMPLETE")
    print("="*50)

if __name__ == "__main__":
    test_cases()
    
    print("\nTo debug your specific issue:")
    print("1. Run this script to see if highlighting works in isolation")
    print("2. Check the browser console for the debug logs we added")
    print("3. Check the server logs for the backend debug messages")
    print("4. Compare the evaluated_points structure with the test cases above")
